package com.ruoyi.system.service.impl;

import com.ruoyi.common.security.context.PreTenantContext;
import com.ruoyi.system.api.domain.SysUser;
import com.ruoyi.system.mapper.SysUserMapper;
import com.ruoyi.system.service.SysPasswordCryptoService;
import com.ruoyi.system.service.impl.SysUserSignServiceImpl;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 密码加密初始化服务实现类
 * 提供批量加密现有用户密码的功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class SysPasswordCryptoInitServiceImpl {

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysPasswordCryptoService sysPasswordCryptoService;

    @Autowired
    private SysUserSignServiceImpl sysUserSignService;

    @Autowired(required = false)
    private PreTenantContext preTenantContext;

    /**
     * 初始化所有用户密码加密
     *
     * @return 初始化结果统计
     */
    @Transactional(rollbackFor = Exception.class)
    public PasswordCryptoInitResult initAllPasswordCrypto() {
        log.info("开始初始化所有用户密码加密...");

        // 设置app_id上下文，解决PreAppHandler多租户拦截问题
        if (preTenantContext != null) {
            preTenantContext.setCurrentAppId("2");
        }

        PasswordCryptoInitResult result = new PasswordCryptoInitResult();
        
        try {
            // 查询所有有效用户
            SysUser queryUser = new SysUser();
            queryUser.setDelFlag("0"); // 未删除的用户
            List<SysUser> users = sysUserMapper.selectUserSacInit(queryUser);

            log.info("找到 {} 个用户需要初始化密码加密", users.size());
            result.totalCount = users.size();
            
            for (SysUser user : users) {
                try {
                    // 检查密码是否为BCrypt格式（未加密）
                    if (!sysPasswordCryptoService.isBCryptPassword(user.getPassword())) {
                        result.skippedCount++;
                        log.debug("用户密码已加密，跳过 - 用户ID: {}, 用户名: {}", user.getUserId(), user.getUserName());
                        continue;
                    }

                    // 对密码进行SAC加密
                    String encryptedPassword = sysPasswordCryptoService.encryptPassword(user.getPassword());
                    
                    // 更新数据库中的密码
                    int updateResult = sysUserMapper.resetUserPwd(user.getUserName(), encryptedPassword);
                    
                    if (updateResult > 0) {
                        // 重新签名用户数据（使用加密后的密码进行签名）
                        try {
                            sysUserSignService.signUserData(
                                user.getUserId(),
                                user.getUserName(),
                                encryptedPassword,
                                user.getPhonenumber()
                            );
                        } catch (Exception e) {
                            log.warn("用户密码加密后重新签名失败，用户ID：{}，用户名：{}，错误：{}", 
                                user.getUserId(), user.getUserName(), e.getMessage());
                        }
                        
                        result.successCount++;
                        log.info("用户密码加密成功 - 用户ID: {}, 用户名: {}", user.getUserId(), user.getUserName());
                    } else {
                        result.failCount++;
                        log.error("用户密码加密失败，数据库更新失败 - 用户ID: {}, 用户名: {}", user.getUserId(), user.getUserName());
                    }
                    
                } catch (Exception e) {
                    result.failCount++;
                    log.error("用户密码加密失败，用户ID：{}，用户名：{}，错误：{}", 
                        user.getUserId(), user.getUserName(), e.getMessage(), e);
                }
                
                // 每处理100个用户输出一次进度
                if ((result.successCount + result.failCount + result.skippedCount) % 100 == 0) {
                    log.info("密码加密进度 - 总数: {}, 成功: {}, 失败: {}, 跳过: {}", 
                        result.totalCount, result.successCount, result.failCount, result.skippedCount);
                }
            }
            
            log.info("用户密码加密初始化完成 - 总数: {}, 成功: {}, 失败: {}, 跳过: {}", 
                result.totalCount, result.successCount, result.failCount, result.skippedCount);
            
        } catch (Exception e) {
            log.error("用户密码加密初始化异常：{}", e.getMessage(), e);
            result.errorMessage = e.getMessage();
        }
        
        return result;
    }

    /**
     * 分批初始化用户密码加密
     *
     * @param batchSize 每批处理的数量
     * @param maxBatches 最大批次数，0表示处理全部
     * @return 初始化结果统计
     */
    @Transactional(rollbackFor = Exception.class)
    public PasswordCryptoInitResult initPasswordCryptoBatch(int batchSize, int maxBatches) {
        log.info("开始分批初始化用户密码加密，批次大小: {}, 最大批次: {}", batchSize, maxBatches == 0 ? "全部" : maxBatches);

        // 设置app_id上下文
        if (preTenantContext != null) {
            preTenantContext.setCurrentAppId("2");
        }

        PasswordCryptoInitResult result = new PasswordCryptoInitResult();
        
        try {
            // 查询所有有效用户总数
            SysUser queryUser = new SysUser();
            queryUser.setDelFlag("0");
            List<SysUser> allUsers = sysUserMapper.selectUserSacInit(queryUser);
            result.totalCount = allUsers.size();

            log.info("找到 {} 个用户需要处理", result.totalCount);

            int processedBatches = 0;
            int startIndex = 0;

            while (startIndex < allUsers.size() && (maxBatches == 0 || processedBatches < maxBatches)) {
                int endIndex = Math.min(startIndex + batchSize, allUsers.size());
                List<SysUser> batchUsers = allUsers.subList(startIndex, endIndex);

                log.info("处理第 {} 批，用户范围: {} - {}", processedBatches + 1, startIndex + 1, endIndex);

                for (SysUser user : batchUsers) {
                    try {
                        // 检查密码是否为BCrypt格式（未加密）
                        if (!sysPasswordCryptoService.isBCryptPassword(user.getPassword())) {
                            result.skippedCount++;
                            continue;
                        }

                        // 对密码进行SAC加密
                        String encryptedPassword = sysPasswordCryptoService.encryptPassword(user.getPassword());
                        
                        // 更新数据库中的密码
                        int updateResult = sysUserMapper.resetUserPwd(user.getUserName(), encryptedPassword);
                        
                        if (updateResult > 0) {
                            // 重新签名用户数据
                            try {
                                sysUserSignService.signUserData(
                                    user.getUserId(),
                                    user.getUserName(),
                                    encryptedPassword,
                                    user.getPhonenumber()
                                );
                            } catch (Exception e) {
                                log.warn("用户密码加密后重新签名失败，用户ID：{}，错误：{}", user.getUserId(), e.getMessage());
                            }
                            
                            result.successCount++;
                        } else {
                            result.failCount++;
                            log.error("用户密码加密失败，数据库更新失败 - 用户ID: {}", user.getUserId());
                        }
                        
                    } catch (Exception e) {
                        result.failCount++;
                        log.error("用户密码加密失败，用户ID：{}，错误：{}", user.getUserId(), e.getMessage());
                    }
                }

                processedBatches++;
                startIndex = endIndex;

                log.info("第 {} 批处理完成，当前进度 - 成功: {}, 失败: {}, 跳过: {}", 
                    processedBatches, result.successCount, result.failCount, result.skippedCount);
            }
            
            log.info("分批密码加密初始化完成 - 总数: {}, 处理批次: {}, 成功: {}, 失败: {}, 跳过: {}", 
                result.totalCount, processedBatches, result.successCount, result.failCount, result.skippedCount);
            
        } catch (Exception e) {
            log.error("分批密码加密初始化异常：{}", e.getMessage(), e);
            result.errorMessage = e.getMessage();
        }
        
        return result;
    }

    /**
     * 密码加密初始化结果统计
     */
    @Data
    public static class PasswordCryptoInitResult {
        /** 总用户数 */
        public int totalCount = 0;
        /** 成功加密数 */
        public int successCount = 0;
        /** 失败数 */
        public int failCount = 0;
        /** 跳过数（已加密） */
        public int skippedCount = 0;
        /** 错误信息 */
        public String errorMessage;
    }
}
