package com.ruoyi.system.service;

import com.ruoyi.sac.domain.api.DecryptRequestForApi;
import com.ruoyi.sac.domain.api.EncryptRequestForApi;
import com.ruoyi.sac.domain.response.DecryptResponse;
import com.ruoyi.sac.domain.response.EncryptResponse;
import com.ruoyi.sac.service.CryptoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;

/**
 * 用户密码加密服务
 * 提供sys_user表password字段的商用密码加密和解密功能
 * 在原有BCrypt加密基础上，增加SAC商用密码二次加密
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class SysPasswordCryptoService {

    @Autowired
    private CryptoService cryptoService;

    /**
     * BCrypt密码前缀，用于识别未进行SAC加密的密码
     */
    private static final String BCRYPT_PREFIX = "$2a$10$";

    /**
     * 对密码进行SAC商用密码加密
     *
     * @param password 原始密码（通常是BCrypt加密后的密码）
     * @return 加密后的密码，如果加密失败则返回原密码
     */
    public String encryptPassword(String password) {
        if (!StringUtils.hasText(password)) {
            return password;
        }

        // 检查是否为未加密的BCrypt密码（以$2a$10$开头）
        if (!isBCryptPassword(password)) {
            log.debug("密码不是BCrypt格式或已经进行过SAC加密，跳过加密: {}", maskPassword(password));
            return password;
        }

        try {
            log.info("开始对密码进行SAC加密，原密码长度: {}", password.length());

            // 将密码转换为HEX编码
            String hexPassword = stringToHex(password);
            log.debug("密码HEX编码后长度: {}", hexPassword.length());

            EncryptRequestForApi request = new EncryptRequestForApi();
            request.setData(hexPassword);

            EncryptResponse response = cryptoService.encryptForApi(request);

            if (response != null && response.getCode() != null && response.getCode() == 200
                && response.getData() != null && StringUtils.hasText(response.getData().getEncData())) {

                String encryptedPassword = response.getData().getEncData();
                log.info("密码SAC加密成功，加密后长度: {}", encryptedPassword.length());
                return encryptedPassword;
            } else {
                log.error("密码SAC加密失败，响应码: {}, 响应消息: {}", 
                    response != null ? response.getCode() : "null",
                    response != null ? response.getMsg() : "null");
                return password; // 加密失败时返回原密码
            }
        } catch (Exception e) {
            log.error("密码SAC加密异常: {}", e.getMessage(), e);
            return password; // 加密异常时返回原密码
        }
    }

    /**
     * 对密码进行SAC商用密码解密
     *
     * @param encryptedPassword 加密后的密码
     * @return 解密后的密码，如果解密失败则返回原密码
     */
    public String decryptPassword(String encryptedPassword) {
        if (!StringUtils.hasText(encryptedPassword)) {
            return encryptedPassword;
        }

        // 检查是否是BCrypt密码（未加密的）
        if (isBCryptPassword(encryptedPassword)) {
            log.debug("密码是BCrypt格式，未进行SAC加密，直接返回: {}", maskPassword(encryptedPassword));
            return encryptedPassword;
        }

        try {
            log.info("开始对密码进行SAC解密，密文长度: {}", encryptedPassword.length());

            DecryptRequestForApi request = new DecryptRequestForApi();
            request.setData(encryptedPassword);

            DecryptResponse response = cryptoService.decryptForApi(request);

            if (response != null && response.getCode() != null && response.getCode() == 200
                && response.getData() != null && StringUtils.hasText(response.getData().getDecData())) {

                // 解密返回的是HEX编码的数据，需要转换回原始字符串
                String hexDecryptedData = response.getData().getDecData();
                String decryptedPassword = hexToString(hexDecryptedData);
                log.info("密码SAC解密成功，解密后长度: {}", decryptedPassword.length());
                return decryptedPassword;
            } else {
                log.error("密码SAC解密失败，响应码: {}, 响应消息: {}", 
                    response != null ? response.getCode() : "null",
                    response != null ? response.getMsg() : "null");
                return encryptedPassword; // 解密失败时返回原密码
            }
        } catch (Exception e) {
            log.error("密码SAC解密异常: {}", e.getMessage(), e);
            return encryptedPassword; // 解密异常时返回原密码
        }
    }

    /**
     * 检查密码是否为BCrypt格式（未进行SAC加密）
     *
     * @param password 密码
     * @return true表示是BCrypt格式，false表示已进行SAC加密
     */
    public boolean isBCryptPassword(String password) {
        return StringUtils.hasText(password) && password.startsWith(BCRYPT_PREFIX);
    }

    /**
     * 检查密码是否已经进行过SAC加密
     *
     * @param password 密码
     * @return true表示已加密，false表示未加密
     */
    public boolean isAlreadyEncrypted(String password) {
        return !isBCryptPassword(password);
    }

    /**
     * 掩码显示密码，用于日志记录
     * 
     * @param password 密码
     * @return 掩码后的密码
     */
    private String maskPassword(String password) {
        if (!StringUtils.hasText(password)) {
            return "null";
        }
        if (password.length() <= 8) {
            return "****";
        }
        return password.substring(0, 4) + "****" + password.substring(password.length() - 4);
    }

    /**
     * 批量加密密码列表
     * 
     * @param passwords 密码列表
     * @return 加密后的密码列表
     */
    public java.util.List<String> batchEncryptPasswords(java.util.List<String> passwords) {
        if (passwords == null || passwords.isEmpty()) {
            return passwords;
        }

        java.util.List<String> encryptedPasswords = new java.util.ArrayList<>();
        for (String password : passwords) {
            encryptedPasswords.add(encryptPassword(password));
        }
        return encryptedPasswords;
    }

    /**
     * 批量解密密码列表
     * 
     * @param encryptedPasswords 加密后的密码列表
     * @return 解密后的密码列表
     */
    public java.util.List<String> batchDecryptPasswords(java.util.List<String> encryptedPasswords) {
        if (encryptedPasswords == null || encryptedPasswords.isEmpty()) {
            return encryptedPasswords;
        }

        java.util.List<String> decryptedPasswords = new java.util.ArrayList<>();
        for (String encryptedPassword : encryptedPasswords) {
            decryptedPasswords.add(decryptPassword(encryptedPassword));
        }
        return decryptedPasswords;
    }

    /**
     * 将字符串转换为HEX编码
     *
     * @param str 原始字符串
     * @return HEX编码字符串
     */
    private String stringToHex(String str) {
        if (str == null) {
            return null;
        }

        byte[] bytes = str.getBytes(StandardCharsets.UTF_8);
        StringBuilder hexString = new StringBuilder();

        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }

        return hexString.toString().toUpperCase();
    }

    /**
     * 将HEX编码字符串转换为原始字符串
     *
     * @param hexStr HEX编码字符串
     * @return 原始字符串
     */
    private String hexToString(String hexStr) {
        if (hexStr == null || hexStr.length() % 2 != 0) {
            return null;
        }

        byte[] bytes = new byte[hexStr.length() / 2];

        for (int i = 0; i < hexStr.length(); i += 2) {
            String hexByte = hexStr.substring(i, i + 2);
            bytes[i / 2] = (byte) Integer.parseInt(hexByte, 16);
        }

        return new String(bytes, StandardCharsets.UTF_8);
    }
}
